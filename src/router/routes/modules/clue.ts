import { DEFAULT_LAYOUT } from '@/router/routes/base';
import { AppRouteRecordRaw } from '../types';

const RouterConfig: AppRouteRecordRaw = {
  path: '/clue',
  name: 'clue',
  redirect: '/clue/sale-clue-list',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '线索',
    roles: ['*'],
    requiresAuth: true,
    order: 100,
    icon: 'icon-send',
  },
  children: [
    // {
    //  path: 'all-clue-list',
    //  name: 'all-clue-list',
    //  component: () =>
    //    import('@/views/travel-clue/all-clue-list/all-clue-list.vue'),
    //  meta: {
    //    locale: '线索公海',
    //    roles: ['*'],
    //    requiresAuth: true,
    //  },
    // },

    {
      path: 'clue-setting',
      name: 'clue-setting',
      component: () =>
        import('@/views/travel-clue/clue-setting/clue-setting.vue'),
      meta: {
        locale: '线索收集配置',
        suffix: '(测试)',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-tool',
      },
    },
    {
      path: 'replay-template',
      name: 'replay-template',
      component: () =>
        import('@/views/travel-clue/replay-template/replay-template.vue'),
      meta: {
        locale: '回复模板配置',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-settings',
      },
    },
    // 模版统计
    {
      path: 'replay-template-total',
      name: 'replay-template-total',
      component: () =>
        import('@/views/travel-clue/replay-template/replay-template-total.vue'),
      meta: {
        locale: '回复模板统计',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-settings',
      },
    },
    {
      path: 'ai-service',
      name: 'ai-service',
      component: () => import('@/views/travel-clue/ai-service/ai-service.vue'),
      meta: {
        locale: 'AI客服',
        roles: [1, 15],
        requiresAuth: true,
        // icon: 'icon-customer-service',
      },
    },
    {
      path: 'sale-clue-list',
      name: 'sale-clue-list',
      component: () => import('@/views/travel-clue/clue-management/main.vue'),
      meta: {
        locale: '线索管理',
        roles: ['*'],
        requiresAuth: true,
        // icon: 'icon-trophy',
      },
    },
    // {
    //  path: 'sale-clue-list-two',
    //  name: 'sale-clue-list-two',
    //  component: () =>
    //    import('@/views/travel-clue/sale-clue-list-two/sale-clue-list-two.vue'),
    //  meta: {
    //    locale: '留资线索',
    //    roles: ['*'],
    //    requiresAuth: true,
    //    icon: 'icon-trophy',
    //  },
    // },
  ],
};
export default RouterConfig;
