<!--
  聊天窗口组件 - 线索管理右侧聊天区域
  支持消息显示、发送、状态管理等功能，包含线索详情展开模块
  集成 API 数据获取和心跳检测机制

  心跳检测功能特性：
  - 5秒间隔轮询当前选中媒体账号的聊天消息
  - 确保API参数（account_id、platform、self_account_id）与当前选中账号一致
  - 避免不同账号间的消息串扰，每次切换账号时重新启动心跳检测
  - 组件卸载时自动清理定时器，防止内存泄漏
  - 只更新当前活跃聊天窗口的消息，不影响其他账号的消息列表

  智能滚动功能特性：
  - 通过消息ID比较检测新消息到达
  - 用户在底部时（50px容忍度）自动平滑滚动到新消息
  - 用户查看历史消息时不强制滚动，显示新消息提示按钮
  - 支持点击提示按钮手动跳转到最新消息
  - 切换账号时重置滚动状态，确保消息隔离

  消息定位功能特性：
  - 支持基于消息发送时间的精确定位功能
  - 使用平滑滚动效果定位到目标消息
  - 对目标消息添加临时高亮效果（2.5秒后自动消失）
  - 支持多种时间格式（字符串、时间戳）
  - 提供友好的错误提示和状态反馈
  - 通过 defineExpose 暴露 scrollToMessageByTime 方法供父组件调用

  公共方法：
  - scrollToMessageByTime(sendTime: string | number): boolean - 基于时间定位消息
  - scrollToBottom(smooth?: boolean): void - 滚动到底部
  - scrollToNewMessages(): void - 滚动到新消息
  - clearHighlight(): void - 清除高亮效果

  使用示例：
  // 在父组件中通过 ref 调用
  const chatWindowRef = ref<InstanceType<typeof ChatWindow>>();
  chatWindowRef.value?.scrollToMessageByTime('2024-01-01 12:30:45');
  chatWindowRef.value?.scrollToMessageByTime(1704085845000); // 时间戳格式
-->
<template>
  <div class="chat-area">
    <div v-if="selectedMessage" class="chat-main-container">
      <!-- 聊天窗口容器 -->
      <div
        class="chat-container"
        :class="{ 'with-detail-panel': showDetailPanel }"
      >
        <!-- 全屏加载遮罩层 -->
        <div v-if="loading" class="chat-fullscreen-loading">
          <div class="loading-overlay">
            <!-- 顶部用户信息区域（加载时也显示） -->
            <div class="loading-header">
              <div class="chat-user-info">
                <div class="chat-user-details">
                  <span class="chat-username">{{
                    selectedMessage.username
                  }}</span>
                </div>
              </div>
              <div class="chat-actions"> </div>
            </div>

            <!-- 加载内容区域 -->
            <div class="loading-content">
              <div class="loading-container">
                <!-- 骨架屏动画 -->
                <div class="skeleton-messages">
                  <!-- 接收消息骨架屏 -->
                  <div class="skeleton-message received">
                    <div class="skeleton-avatar"></div>
                    <div class="skeleton-content">
                      <div class="skeleton-header">
                        <div class="skeleton-username"></div>
                        <div class="skeleton-time"></div>
                      </div>
                      <div class="skeleton-bubble received">
                        <div class="skeleton-text short"></div>
                        <div class="skeleton-text medium"></div>
                      </div>
                    </div>
                  </div>

                  <!-- 发送消息骨架屏 -->
                  <div class="skeleton-message sent">
                    <div class="skeleton-content">
                      <div class="skeleton-header">
                        <div class="skeleton-username"></div>
                        <div class="skeleton-time"></div>
                      </div>
                      <div class="skeleton-bubble sent">
                        <div class="skeleton-text long"></div>
                      </div>
                    </div>
                    <div class="skeleton-avatar"></div>
                  </div>

                  <!-- 接收消息骨架屏 -->
                  <div class="skeleton-message received">
                    <div class="skeleton-avatar"></div>
                    <div class="skeleton-content">
                      <div class="skeleton-header">
                        <div class="skeleton-username"></div>
                        <div class="skeleton-time"></div>
                      </div>
                      <div class="skeleton-bubble received">
                        <div class="skeleton-text medium"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 加载时的输入区域占位 -->
            <div class="loading-input-placeholder">
              <div class="input-placeholder-content">
                <div class="placeholder-toolbar"></div>
                <div class="placeholder-textarea"></div>
                <div class="placeholder-send-btn"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 正常聊天界面 -->
        <div v-else class="chat-normal-view">
          <!-- 聊天头部 -->
          <div class="chat-header">
            <div class="chat-user-info">
              <div class="chat-user-details">
                <span class="chat-username">{{
                  selectedMessage.username
                }}</span>
              </div>
            </div>
            <div class="chat-actions">
              <!-- 预留操作按钮 -->
              <a-button
                v-if="showDetailPanel"
                type="text"
                size="small"
                :class="{ active: showDetailPanel }"
                @click="toggleDetailPanel"
              >
                <template #icon>
                  <icon-menu-unfold />
                </template>
              </a-button>
              <a-button
                v-else
                type="text"
                size="small"
                @click="toggleDetailPanel"
              >
                <template #icon>
                  <icon-menu-fold />
                </template>
              </a-button>
            </div>
          </div>

          <!-- 聊天内容区域 -->
          <div class="chat-content">
            <!-- 聊天消息列表 - 全面重构，包含用户信息、状态和时间 -->
            <div ref="messagesContainerRef" class="chat-messages">
              <!-- 新消息提示浮层 -->
              <div
                v-if="showNewMessageTip"
                class="new-message-tip"
                @click="scrollToNewMessages"
              >
                <div class="tip-content">
                  <icon-down class="tip-icon" />
                  <span class="tip-text">
                    {{
                      newMessageCount > 0
                        ? `${newMessageCount} 条新消息`
                        : '有新消息'
                    }}
                  </span>
                </div>
              </div>
              <!-- 错误状态显示 -->
              <div
                v-if="error && chatMessages.length === 0"
                class="chat-error-state"
              >
                <div class="error-content">
                  <icon-exclamation class="error-icon" />
                  <span class="error-text">{{ error }}</span>
                  <a-button size="mini" type="primary" @click="handleRetry">
                    重试
                  </a-button>
                </div>
              </div>
              <div
                v-for="msg in chatMessages"
                :key="msg.id"
                :ref="(el) => setMessageRef(el, msg)"
                :class="[
                  'chat-message-wrapper',
                  msg.type,
                  { 'message-highlighted': highlightedMessageId === msg.id },
                ]"
              >
                <!-- 接收消息布局 -->
                <div
                  v-if="msg.type === 'received'"
                  class="message-row received"
                >
                  <div class="message-avatar">
                    <a-avatar
                      :size="32"
                      :style="
                        getAvatarStyle(selectedMessage?.username || '用户')
                      "
                    >
                      <img
                        v-if="selectedMessage?.avatar"
                        :src="selectedMessage?.avatar"
                        alt="用户头像"
                        @error="handleAvatarError"
                      />
                      <span v-else>
                        {{ selectedMessage?.username?.charAt(0) || '用' }}
                      </span>
                    </a-avatar>
                  </div>
                  <div class="message-content">
                    <div class="message-header">
                      <span class="username">{{
                        selectedMessage?.username || '用户'
                      }}</span>
                      <span class="message-time">{{ msg.time }}</span>
                    </div>
                    <div class="message-bubble received">
                      <!-- 根据消息类型渲染不同内容 -->
                      <template v-if="msg.message_type === 'image'">
                        <a-image
                          :src="msg.content"
                          :height="150"
                          style="min-width: 100px"
                          fit="contain"
                          show-loader
                          preview
                          class="message-image"
                          :alt="`图片消息 - ${msg.time}`"
                          @error="handleImageError"
                        />
                      </template>
                      <template v-else>
                        <chat-emoji-text :content="msg.content" />
                      </template>

                      <!-- 接收消息状态图标（在气泡右侧） -->
                      <div class="message-status-icon">
                        <icon-check-circle
                          v-if="getMessageStatusIcon(msg) === 'IconCheck'"
                          class="success_icon"
                          size="14"
                        />
                        <icon-loading
                          v-else-if="
                            getMessageStatusIcon(msg) === 'IconLoading'
                          "
                          :style="{
                            color: getMessageStatusColor(msg),
                            fontSize: '14px',
                          }"
                          spin
                        />
                        <icon-exclamation
                          v-else-if="
                            getMessageStatusIcon(msg) === 'IconExclamation'
                          "
                          :style="{
                            color: getMessageStatusColor(msg),
                            fontSize: '14px',
                          }"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 发送消息布局 -->
                <div v-else class="message-row sent">
                  <div class="message-content">
                    <div class="message-header">
                      <span class="username">{{
                        getSentMessageUsername()
                      }}</span>
                      <span class="message-time">{{ msg.time }}</span>
                    </div>
                    <div class="message-bubble sent">
                      <!-- 根据消息类型渲染不同内容 -->
                      <template v-if="msg.message_type === 'image'">
                        <a-image
                          :src="msg.content"
                          fit="contain"
                          style="min-width: 100px"
                          show-loader
                          preview
                          :height="150"
                          class="message-image"
                          :alt="`图片消息 - ${msg.time}`"
                          @error="handleImageError"
                        />
                      </template>
                      <template v-else>
                        <chat-emoji-text :content="msg.content" />
                      </template>

                      <!-- 发送消息状态图标（在气泡左侧） -->
                      <div class="message-status-icon">
                        <icon-check-circle
                          v-if="getMessageStatusIcon(msg) === 'IconCheck'"
                          class="success_icon"
                          size="14"
                        />
                        <icon-loading
                          v-else-if="
                            getMessageStatusIcon(msg) === 'IconLoading'
                          "
                          :style="{
                            color: getMessageStatusColor(msg),
                            fontSize: '14px',
                          }"
                          spin
                        />
                        <icon-exclamation
                          v-else-if="
                            getMessageStatusIcon(msg) === 'IconExclamation'
                          "
                          :style="{
                            color: getMessageStatusColor(msg),
                            fontSize: '14px',
                          }"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="message-avatar">
                    <a-avatar
                      :size="32"
                      :style="getAvatarStyle(selectedAccount?.name || '客服')"
                      @error="handleAvatarError"
                    >
                      <img
                        v-if="getSentMessageAvatar()"
                        :src="getSentMessageAvatar()"
                        alt="客服头像"
                        @error="handleAvatarError"
                      />
                      <span v-else>
                        {{ getSentMessageAvatarText() }}
                      </span>
                    </a-avatar>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 聊天输入模块 - 整合优化，统一设计风格 -->
          <div class="chat-input-module">
            <!-- 输入工具栏与文本区域整合 -->
            <div class="input-main-area">
              <div class="input-toolbar">
                <chat-emoji
                  class="emoji-btn"
                  :platform="chatParams.platform"
                  @select="selectEmoji"
                >
                  <img
                    class="toolbar-btn"
                    src="@/assets/images/msg-smile.png"
                    alt="表情"
                  />
                </chat-emoji>
                <!-- 图片上传按钮 -->
                <!-- <a-upload
                  action="/api/uploadFile"
                  :show-file-list="false"
                  :data="{ type: 'send_msg_image' }"
                  accept="image/*"
                  class="upload-btn"
                  @change="uploadChange"
                  @success="uploadSuccess"
                >
                  <template #upload-button>
                    <img
                      class="toolbar-btn"
                      src="@/assets/images/msg-photo.png"
                      alt="图片"
                    />
                  </template>
                </a-upload> -->
              </div>

              <div class="input-content">
                <a-textarea
                  ref="inputRef"
                  :model-value="inputMessage"
                  placeholder="按回车键发送信息，Shift+回车换行..."
                  class="message-textarea"
                  :auto-size="{ minRows: 3, maxRows: 6 }"
                  :allow-clear="false"
                  @update:model-value="handleInputChange"
                  @keydown.enter="handleSendMessage"
                />
                <div class="send-action">
                  <a-button
                    type="primary"
                    size="small"
                    :disabled="isSendButtonDisabled"
                    :loading="sendingStatus === ChatMessageSendStatus.SENDING"
                    :class="{
                      success: sendingStatus === ChatMessageSendStatus.SUCCESS,
                      failed: sendingStatus === ChatMessageSendStatus.FAILED,
                    }"
                    @click="handleSendMessage"
                  >
                    <template #icon>
                      <icon-loading
                        v-if="sendingStatus === ChatMessageSendStatus.SENDING"
                      />
                      <icon-check
                        v-else-if="
                          sendingStatus === ChatMessageSendStatus.SUCCESS
                        "
                      />
                      <icon-exclamation
                        v-else-if="
                          sendingStatus === ChatMessageSendStatus.FAILED
                        "
                      />
                      <icon-send v-else />
                    </template>
                    {{ sendButtonText }}
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 线索详情展开模块 - 使用独立组件 -->
      <clue-detail-panel
        v-if="showDetailPanel"
        :selected-message="selectedMessage"
        :selected-account="selectedAccount"
        :visible="showDetailPanel"
        @close="toggleDetailPanel"
      />
    </div>

    <!-- 未选择消息时的占位内容 -->
    <div v-else class="chat-placeholder">
      <div class="placeholder-content">
        <icon-message class="placeholder-icon" />
        <p>选择一个对话开始聊天</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue';
  import {
    IconMore,
    IconMessage,
    IconImage,
    IconAttachment,
    IconGift,
    IconSend,
    IconLoading,
    IconCheck,
    IconExclamation,
    IconCheckCircle,
    IconClose,
    IconDown,
  } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import type {
    AccountInfo,
    MessageItem,
    ChatMessage,
    ChatParams,
    ApiChatRecord,
  } from '@/views/travel-clue/clue-management/types';
  import ChatEmoji from '@/views/travel-clue/sale-clue-list/chat-emoji.vue';
  import ChatEmojiText from '@/views/travel-clue/sale-clue-list/chat-emoji-text.vue';
  import { setCaretPosition } from '@/utils/util';
  import request from '@/api/request';
  import dayjs from 'dayjs';
  import { SendStatusUtils, SendStatus } from '@/utils/sendStatus';
  import ClueDetailPanel from './ClueDetailPanel.vue';

  // 消息发送状态枚举
  // eslint-disable-next-line no-shadow
  enum ChatMessageSendStatus {
    IDLE = 'idle',
    SENDING = 'sending',
    SUCCESS = 'success',
    FAILED = 'failed',
  }

  // 定义 Props 和 Emits
  const props = defineProps({
    selectedMessage: {
      type: Object,
      default: null,
    },
    selectedAccount: {
      type: Object,
      default: null,
    },
  });

  const emit = defineEmits([
    'avatarError',
    'retryMessage',
    'messagesLoaded',
    'loadError',
  ]);

  // 组件引用
  const inputRef = ref<any>(null);
  const messagesContainerRef = ref<HTMLElement | null>(null);

  // 详情面板显示状态
  const showDetailPanel = ref(true);

  // 消息发送状态管理
  const sendingStatus = ref<any>(null);

  // 表情包面板显示状态
  const showEmojiPanel = ref(false);

  // 内部数据管理 - 从 props 移动到组件内部
  const chatMessages = ref<any[]>([]);
  const inputMessage = ref('');
  const loading = ref(false);
  const error = ref<string | null>(null); // 错误状态

  // 心跳检测相关
  const heartbeatTimer = ref<number | null>(null);
  const HEARTBEAT_INTERVAL = 5000; // 5秒间隔
  const currentHeartbeatParams = ref<{
    account_id: string;
    platform: string;
    self_account_id: string;
  } | null>(null); // 当前心跳检测的参数，用于避免消息串扰

  // 智能滚动相关
  const SCROLL_THRESHOLD = 50; // 判断用户是否在底部的阈值（像素）
  const lastMessageIds = ref<Set<string | number>>(new Set()); // 记录上次的消息ID集合
  const showNewMessageTip = ref(false); // 显示新消息提示
  const newMessageCount = ref(0); // 新消息数量

  // 消息定位相关
  const messageRefs = ref<Map<string | number, HTMLElement>>(new Map()); // 消息元素引用映射
  const highlightedMessageId = ref<string | number | null>(null); // 当前高亮的消息ID
  const highlightTimer = ref<number | null>(null); // 高亮效果定时器

  // 本地输入框内容（用于表情包插入）
  const localInputMessage = ref('');

  // 聊天参数计算属性
  const chatParams = computed<ChatParams>(() => ({
    platform: props.selectedMessage?.platform || '',
    account_id: props.selectedMessage?.account_id || '',
    clue_id: props.selectedMessage?.clue_id || '',
  }));

  // 加载模拟聊天消息数据
  const loadMockChatMessages = async () => {
    const mockData = [];

    // 模拟网络延迟
    await new Promise((resolve) => {
      setTimeout(resolve, 500);
    });
    chatMessages.value = mockData;
    console.log('✅ 模拟聊天数据加载完成');
  };

  // 格式化时间显示
  const formatTime = (timeStr: string): string => {
    if (!timeStr) return '';

    try {
      return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss');
    } catch (e) {
      console.warn('时间格式化失败:', timeStr, e);
      return timeStr;
    }
  };

  // 检查用户是否在聊天窗口底部
  const isUserAtBottom = (): boolean => {
    if (!messagesContainerRef.value) return true;

    const container = messagesContainerRef.value;
    const { scrollTop } = container;
    const { scrollHeight } = container;
    const { clientHeight } = container;

    // 计算距离底部的距离
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

    return distanceFromBottom <= SCROLL_THRESHOLD;
  };

  // 滚动到消息列表底部（支持平滑滚动）
  const scrollToBottom = (smooth = false) => {
    if (messagesContainerRef.value) {
      messagesContainerRef.value.scrollTo({
        top: messagesContainerRef.value.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto',
      });
    }
  };

  // 检测新消息并返回新消息信息
  const detectNewMessages = (
    newMessages: any[]
  ): {
    hasNew: boolean;
    newCount: number;
    newMessageIds: Set<string | number>;
  } => {
    const currentMessageIds = new Set(newMessages.map((msg) => msg.id));
    const previousIds = lastMessageIds.value;

    // 找出新增的消息ID
    const newIds = new Set(
      [...currentMessageIds].filter((id) => !previousIds.has(id))
    );

    return {
      hasNew: newIds.size > 0,
      newCount: newIds.size,
      newMessageIds: currentMessageIds,
    };
  };

  // 智能滚动处理函数
  const handleSmartScroll = (newMessages: any[], isHeartbeat = false) => {
    if (!isHeartbeat) {
      // 非心跳检测的情况（如初始加载、用户发送消息），直接滚动到底部
      scrollToBottom(false);
      lastMessageIds.value = new Set(newMessages.map((msg) => msg.id));
      showNewMessageTip.value = false;
      newMessageCount.value = 0;
      return;
    }

    // 心跳检测的情况，需要智能判断
    const { hasNew, newCount, newMessageIds } = detectNewMessages(newMessages);

    if (!hasNew) {
      // 没有新消息，不需要处理
      return;
    }

    console.log(`💬 检测到 ${newCount} 条新消息`);

    // 更新消息ID记录
    lastMessageIds.value = newMessageIds;

    // 检查用户是否在底部
    const userAtBottom = isUserAtBottom();

    if (userAtBottom) {
      // 用户在底部，自动滚动到最新消息
      console.log('📍 用户在底部，自动滚动到最新消息');
      scrollToBottom(true); // 使用平滑滚动
      showNewMessageTip.value = false;
      newMessageCount.value = 0;
    } else {
      // 用户不在底部，显示新消息提示
      console.log('📍 用户不在底部，显示新消息提示');
      showNewMessageTip.value = true;
      newMessageCount.value += newCount;
    }
  };

  // 用户点击新消息提示时滚动到底部
  const scrollToNewMessages = () => {
    scrollToBottom(true);
    showNewMessageTip.value = false;
    newMessageCount.value = 0;
  };

  // 设置消息元素引用
  const setMessageRef = (el: HTMLElement | null, msg: any) => {
    if (el && msg) {
      messageRefs.value.set(msg.id, el);
    }
  };

  // 清除高亮效果
  const clearHighlight = () => {
    if (highlightTimer.value) {
      clearTimeout(highlightTimer.value);
      highlightTimer.value = null;
    }
    highlightedMessageId.value = null;
  };

  // 基于消息发送时间的定位功能
  const scrollToMessageByTime = (sendTime: string | number) => {
    try {
      console.log('🎯 开始定位消息，目标时间:', sendTime);

      // 清除之前的高亮效果
      clearHighlight();

      // 格式化目标时间，支持多种时间格式
      let targetTime: string;
      if (typeof sendTime === 'number') {
        // 时间戳格式
        targetTime = dayjs(sendTime).format('YYYY-MM-DD HH:mm:ss');
      } else {
        // 字符串格式，尝试解析并标准化
        const parsedTime = dayjs(sendTime);
        if (!parsedTime.isValid()) {
          console.error('❌ 无效的时间格式:', sendTime);
          Message.error('无效的时间格式');
          return false;
        }
        targetTime = parsedTime.format('YYYY-MM-DD HH:mm:ss');
      }

      console.log('🔍 查找匹配时间的消息:', targetTime);

      // 在消息列表中查找匹配的消息
      const targetMessage = chatMessages.value.find((msg) => {
        // 比较格式化后的时间字符串
        const msgTime = dayjs(msg.time).format('YYYY-MM-DD HH:mm:ss');
        return msgTime === targetTime;
      });

      if (!targetMessage) {
        console.warn('⚠️ 未找到匹配时间的消息:', targetTime);
        Message.warning('未找到指定时间的消息');
        return false;
      }

      console.log('✅ 找到目标消息:', targetMessage);

      // 获取消息对应的DOM元素
      const messageElement = messageRefs.value.get(targetMessage.id);
      if (!messageElement) {
        console.error('❌ 未找到消息对应的DOM元素:', targetMessage.id);
        Message.error('消息元素未找到，请稍后重试');
        return false;
      }

      // 滚动到目标消息
      messageElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      });

      // 添加高亮效果
      highlightedMessageId.value = targetMessage.id;

      // 设置定时器，2-3秒后移除高亮效果
      highlightTimer.value = window.setTimeout(() => {
        highlightedMessageId.value = null;
        highlightTimer.value = null;
      }, 2500);

      console.log('🎉 消息定位成功，已添加高亮效果');
      return true;
    } catch (errorT: any) {
      console.error('❌ 消息定位过程中发生错误:', errorT);
      Message.error('消息定位失败，请重试');
      return false;
    }
  };

  // 转换 API 数据为组件所需格式
  const transformApiData = (apiData: ApiChatRecord[]): any[] => {
    return apiData.map((item) => ({
      id: item.id,
      type: item.msg_show === 'right' ? 'sent' : 'received',
      content: item.message || '',
      time: formatTime(item.send_time),
      status: (() => {
        // 使用通用状态工具转换状态
        const convertedStatus = SendStatusUtils.convertLegacyStatus(
          item.send_status
        );
        switch (convertedStatus) {
          case SendStatus.SENDING:
            return 'sending';
          case SendStatus.SUCCESS:
            return 'sent';
          case SendStatus.FAILED:
            return 'failed';
          case SendStatus.PENDING:
            return 'pending';
          default:
            return 'failed';
        }
      })(),
      send_status: item.send_status, // 保留原始状态值
      message_type: item.message_type || 'text',
      send_user: item.send_user || '用户',
      avatar:
        item.msg_show === 'right'
          ? item.account_profile_photo
          : item.received_user_avatar,
    }));
  };

  // 获取聊天消息列表的 API 函数
  const getChatMessages = async (showLoading = true, isHeartbeat = false) => {
    // 只有当同时存在 selectedMessage 和 selectedAccount 时才调用接口
    if (
      !props.selectedMessage?.account_id ||
      !props.selectedAccount?.self_account_id
    ) {
      chatMessages.value = [];
      error.value = null;
      return;
    }

    // 构建当前请求参数
    const requestParams = {
      account_id: props.selectedMessage.account_id,
      platform: props.selectedMessage.platform,
      self_account_id: props.selectedAccount.self_account_id,
    };

    // 心跳检测时，验证参数是否与当前活跃会话一致，避免消息串扰
    if (isHeartbeat && currentHeartbeatParams.value) {
      const currentParams = currentHeartbeatParams.value;
      if (
        requestParams.account_id !== currentParams.account_id ||
        requestParams.platform !== currentParams.platform ||
        requestParams.self_account_id !== currentParams.self_account_id
      ) {
        console.log('⚠️ 心跳检测参数不匹配，跳过本次请求，避免消息串扰');
        return;
      }
    }
    if (showLoading) {
      // loading.value = true;
    }
    error.value = null;

    try {
      console.log('🔄 开始获取聊天消息列表...', {
        ...requestParams,
        isHeartbeat,
      });

      const response = await request('/api/thread/chat_record', requestParams);

      if (response && response.data) {
        // 再次验证：确保响应时参数仍然匹配当前选中的会话
        if (isHeartbeat && currentHeartbeatParams.value) {
          const currentParams = currentHeartbeatParams.value;
          if (
            requestParams.account_id !== currentParams.account_id ||
            requestParams.platform !== currentParams.platform ||
            requestParams.self_account_id !== currentParams.self_account_id
          ) {
            console.log('⚠️ 响应时参数已变化，丢弃本次心跳检测结果');
            return;
          }
        }

        // 转换 API 数据格式为组件所需格式
        const apiData = response.data.list || response.data || [];
        const transformedMessages = transformApiData(apiData);

        // 只有当前会话匹配时才更新消息列表
        chatMessages.value = transformedMessages;
        console.log(
          `✅ 聊天消息获取成功: ${transformedMessages.length} 条消息${
            isHeartbeat ? ' (心跳检测)' : ''
          }`
        );

        // 发射事件通知父组件
        emit('messagesLoaded', transformedMessages);

        // 使用智能滚动处理
        await nextTick();
        handleSmartScroll(transformedMessages, isHeartbeat);
      } else {
        throw new Error('API返回数据格式错误');
      }
    } catch (err) {
      console.error('❌ 获取聊天消息失败:', err);
      const errorMessage = '获取聊天记录失败，请重试';
      error.value = errorMessage;
      emit('loadError', errorMessage);

      // 不显示错误提示，避免过于频繁的消息干扰（心跳检测时）
      if (showLoading && !isHeartbeat) {
        Message.error(errorMessage);
        // 失败时加载模拟数据
        await loadMockChatMessages();
      }
    } finally {
      if (showLoading) {
        // 最少展示 300ms 的加载动画
        await new Promise((resolve) => {
          setTimeout(resolve, 300);
        });
        loading.value = false;
      }
    }
  };

  // 停止心跳检测
  const stopHeartbeat = () => {
    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value);
      heartbeatTimer.value = null;
      currentHeartbeatParams.value = null; // 清空心跳参数
      console.log('❌ 心跳检测已停止');
    }
  };

  // 启动心跳检测
  const startHeartbeat = () => {
    // 清除现有定时器
    stopHeartbeat();

    // 只有当同时存在 selectedMessage 和 selectedAccount 时才启动心跳检测
    if (
      !props.selectedMessage?.account_id ||
      !props.selectedAccount?.self_account_id
    ) {
      console.log('⚠️ 缺少必要参数，无法启动心跳检测');
      return;
    }

    // 保存当前心跳检测的参数，用于消息隔离验证
    currentHeartbeatParams.value = {
      account_id: props.selectedMessage.account_id,
      platform: props.selectedMessage.platform,
      self_account_id: props.selectedAccount.self_account_id,
    };

    console.log('💓 启动心跳检测，间隔:', HEARTBEAT_INTERVAL / 1000, '秒', {
      account_id: currentHeartbeatParams.value.account_id,
      platform: currentHeartbeatParams.value.platform,
      self_account_id: currentHeartbeatParams.value.self_account_id,
    });

    heartbeatTimer.value = window.setInterval(() => {
      // 静默获取数据，不显示加载状态，标记为心跳检测请求
      getChatMessages(false, true);
    }, HEARTBEAT_INTERVAL);
  };

  // 重试获取数据
  const handleRetry = () => {
    console.log('🔄 用户手动重试获取聊天消息');
    getChatMessages(true, false);
  };

  // 检查心跳检测状态的辅助函数
  const checkHeartbeatStatus = () => {
    const isActive = heartbeatTimer.value !== null;
    const hasParams = currentHeartbeatParams.value !== null;
    const hasSelection = props.selectedMessage && props.selectedAccount;

    console.log('💓 心跳检测状态检查:', {
      isActive,
      hasParams,
      hasSelection,
      currentParams: currentHeartbeatParams.value,
    });

    return { isActive, hasParams, hasSelection };
  };

  // 切换详情面板显示状态
  const toggleDetailPanel = () => {
    showDetailPanel.value = !showDetailPanel.value;
  };

  // 生成用户头像背景色 - 参考飞书设计风格
  const getAvatarStyle = (username: string) => {
    // 飞书风格的背景色调色板
    const colors = [
      '#FF6B6B', // 珊瑚红
      '#4ECDC4', // 青绿色
      '#45B7D1', // 天蓝色
      '#96CEB4', // 薄荷绿
      '#FFEAA7', // 柠檬黄
      '#DDA0DD', // 梅花紫
      '#98D8C8', // 薄荷蓝
      '#F7DC6F', // 金黄色
      '#BB8FCE', // 淡紫色
      '#85C1E9', // 浅蓝色
    ];

    // 根据用户名生成稳定的颜色索引
    let hash = 0;
    for (let i = 0; i < username.length; i += 1) {
      hash = username.charCodeAt(i) + (hash * 31 - hash);
    }
    const colorIndex = Math.abs(hash) % colors.length;

    return {
      backgroundColor: colors[colorIndex],
      color: '#FFFFFF',
      fontWeight: '500',
    };
  };

  // 获取消息状态图标 - 使用通用状态工具
  const getMessageStatusIcon = (msg: any) => {
    // 优先使用原始 send_status 值
    if (msg.send_status !== undefined) {
      const convertedStatus = SendStatusUtils.convertLegacyStatus(
        msg.send_status
      );
      return SendStatusUtils.getIcon(convertedStatus);
    }

    // 兼容旧版本 status 字段
    const status = msg.status || 'sent';
    const convertedStatus = SendStatusUtils.convertLegacyStatus(status);
    return SendStatusUtils.getIcon(convertedStatus);
  };

  // 获取消息状态图标颜色 - 使用通用状态工具
  const getMessageStatusColor = (msg: any) => {
    // 优先使用原始 send_status 值
    if (msg.send_status !== undefined) {
      const convertedStatus = SendStatusUtils.convertLegacyStatus(
        msg.send_status
      );
      return SendStatusUtils.getColor(convertedStatus);
    }

    // 兼容旧版本 status 字段
    const status = msg.status || 'sent';
    const convertedStatus = SendStatusUtils.convertLegacyStatus(status);
    return SendStatusUtils.getColor(convertedStatus);
  };

  // 获取发送消息时的头像URL - 使用当前选中账号的头像
  const getSentMessageAvatar = () => {
    // 优先使用选中账号的头像
    if (props.selectedAccount?.avatar) {
      return props.selectedAccount.avatar;
    }

    // 如果选中账号没有头像，尝试使用原始数据中的头像信息
    if ((props.selectedAccount as any)?.originalData?.avatar_url) {
      return (props.selectedAccount as any).originalData.avatar_url;
    }

    // 如果都没有，返回默认头像
    return '/icons/common/avatar.png';
  };

  // 获取发送消息时的头像文字 - 使用当前选中账号的名称首字符
  const getSentMessageAvatarText = () => {
    // 优先使用选中账号的名称
    if (props.selectedAccount?.name) {
      return props.selectedAccount.name.charAt(0);
    }

    // 如果选中账号没有名称，尝试使用原始数据中的账号名称
    if ((props.selectedAccount as any)?.originalData?.account_name) {
      return (props.selectedAccount as any).originalData.account_name.charAt(0);
    }

    // 默认显示"客"
    return '客';
  };

  // 获取发送消息时的用户名 - 使用当前选中账号的名称
  const getSentMessageUsername = () => {
    // 优先使用选中账号的名称
    if (props.selectedAccount?.name) {
      return props.selectedAccount.name;
    }

    // 如果选中账号没有名称，尝试使用原始数据中的账号名称
    if ((props.selectedAccount as any)?.originalData?.account_name) {
      return (props.selectedAccount as any).originalData.account_name;
    }

    // 默认显示"客服"
    return '客服';
  };

  // 发送消息的 API 函数
  const sendChatMessage = async (messageContent: string) => {
    try {
      console.log('📤 调用发送消息 API:', messageContent);

      // 调用发送消息的 API
      const response = await request('/api/thread/sendDYChatV2', {
        ...chatParams.value,
        message: messageContent,
        type: 'text',
      });

      if (response && response.code === 0) {
        console.log('✅ 消息发送成功');
        return true;
      }
      console.error('❌ 消息发送失败:', response?.msg || '未知错误');
      Message.error(response?.msg || '发送失败');
      return false;
    } catch (err) {
      console.error('❌ 发送消息 API 调用失败:', err);
      Message.error('发送失败，请重试');
      return false;
    }
  };

  // 处理发送消息事件 - 完善发送逻辑
  const handleSendMessage = async (event?: KeyboardEvent) => {
    // 如果是回车键事件，检查是否按住了Shift键
    if (event && event.key === 'Enter') {
      if (event.shiftKey) {
        // Shift+Enter 换行，不发送消息
        return;
      }
      // 阻止默认的换行行为
      event.preventDefault();
    }

    const messageContent = inputMessage.value?.trim();
    if (!messageContent) {
      console.warn('消息内容为空，无法发送');
      return;
    }

    // 检查是否正在发送中
    if (sendingStatus.value === ChatMessageSendStatus.SENDING) {
      console.warn('消息正在发送中，请稍候');
      return;
    }

    // 检查是否有选中的消息（必需的聊天参数）
    if (!props.selectedMessage) {
      console.warn('没有选中的消息，无法发送');
      Message.warning('请先选择一个对话');
      return;
    }

    try {
      // 设置发送状态
      sendingStatus.value = ChatMessageSendStatus.SENDING;
      console.log('📤 开始发送消息:', messageContent);

      // 先添加消息到本地列表（乐观更新）
      const newMessage = {
        id: Date.now(),
        type: 'sent', // 消息显示类型：sent/received
        content: messageContent, // 消息内容
        time: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        }),
        status: 'sending', // 发送状态
        message_type: 'text', // 消息类型：text/image
        send_user: props.selectedAccount?.name || '客服',
      };

      // 添加消息到聊天列表
      chatMessages.value.push(newMessage);

      // 清空输入框
      inputMessage.value = '';
      localInputMessage.value = '';

      // 滚动到底部
      await nextTick();
      scrollToBottom();

      // 调用 API 发送消息
      const success = await sendChatMessage(messageContent);

      if (success) {
        // 发送成功，更新消息状态
        newMessage.status = 'sent';
        sendingStatus.value = ChatMessageSendStatus.SUCCESS;
        console.log('✅ 消息发送成功');
        // 刷新接口
        getChatMessages(false, false);
      } else {
        // 发送失败，更新消息状态
        newMessage.status = 'failed';
        sendingStatus.value = ChatMessageSendStatus.FAILED;
      }
    } catch (err) {
      // 发送失败
      sendingStatus.value = ChatMessageSendStatus.FAILED;
      console.error('❌ 消息发送失败:', err);

      // 更新最后一条消息的状态为失败
      if (chatMessages.value.length > 0) {
        const lastMessage = chatMessages.value[chatMessages.value.length - 1];
        if (lastMessage.status === 'sending') {
          lastMessage.status = 'failed';
        }
      }
    } finally {
      // 重置发送状态
      setTimeout(() => {
        sendingStatus.value = ChatMessageSendStatus.IDLE;
      }, 1000);
    }
  };

  // 处理头像加载错误事件
  const handleAvatarError = (event: Event) => {
    emit('avatarError', event);
  };

  // 处理图片消息加载错误事件
  const handleImageError = (event: Event) => {
    console.warn('图片消息加载失败:', event);
    // 可以在这里添加错误处理逻辑，比如显示默认图片或错误提示
  };

  // 表情包选择处理 - 优化版本
  const selectEmoji = (emoji: string) => {
    if (!inputRef.value?.textareaRef) {
      console.warn('输入框引用不存在');
      return;
    }

    const textareaElement = inputRef.value.textareaRef;
    const currentValue = inputMessage.value || '';
    const cursorPosition =
      textareaElement.selectionStart || currentValue.length;

    // 在光标位置插入表情包
    const newValue =
      currentValue.slice(0, cursorPosition) +
      emoji +
      currentValue.slice(cursorPosition);

    // 更新输入框内容
    inputMessage.value = newValue;
    localInputMessage.value = newValue;

    // 设置光标位置到表情包后面
    nextTick(() => {
      textareaElement.focus();
      const newCursorPosition = cursorPosition + emoji.length;
      textareaElement.setSelectionRange(newCursorPosition, newCursorPosition);
    });

    console.log('😊 表情包已插入:', emoji);
  };

  const uploading = ref(false);
  function uploadChange(fileList: any[], fileItem: any) {
    switch (fileItem.status) {
      case 'init':
        uploading.value = true;
        break;
      case 'done':
      case 'error':
        uploading.value = false;
        break;
      default:
        break;
    }
  }

  function uploadSuccess(file: any) {
    if (file.status === 'done' && file.response?.code === 0) {
      const newMsg = {
        id: Date.now(),
        type: 'sent', // 消息显示类型：sent/received
        content: file.response?.data?.url, // 图片URL作为内容
        time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        status: 'sending', // 发送状态
        message_type: 'image', // 消息类型：image
        send_status: SendStatus.SENDING, // 使用通用状态：发送中
        send_user: props.selectedAccount?.name || '客服',
      };

      // 添加图片消息到聊天列表
      chatMessages.value.push(newMsg);
      nextTick(() => {
        // todo 滚动到底部
        scrollToBottom();
      });
      request('/api/thread/sendDYChatV2', {
        ...chatParams.value,
        image_url: file.response?.data?.file_path,
        type: 'image',
      })
        .then((res) => {
          if (res.code === 0) {
            //  获取最新消息列表
            getChatMessages(false, false);
          }
        })
        .catch(() => {
          Message.error('发送失败');
        });
    } else if (file.status === 'error') {
      Message.error(file.response?.data?.msg || '上传失败');
    }
  }

  // 处理输入框内容变化
  const handleInputChange = (value: string) => {
    inputMessage.value = value;
    localInputMessage.value = value;
  };

  // 重试发送失败的消息
  const retryMessage = (messageId: string) => {
    emit('retryMessage', messageId);
  };

  // 计算发送按钮的禁用状态
  const isSendButtonDisabled = computed(() => {
    return (
      !inputMessage.value?.trim() ||
      sendingStatus.value === ChatMessageSendStatus.SENDING
    );
  });

  // 计算发送按钮的文本
  const sendButtonText = computed(() => {
    switch (sendingStatus.value) {
      case ChatMessageSendStatus.SENDING:
        return '发送中...';
      case ChatMessageSendStatus.SUCCESS:
        return '发送成功';
      case ChatMessageSendStatus.FAILED:
        return '重新发送';
      default:
        return '发送';
    }
  });

  // 监听选中消息和账号变化，自动获取聊天记录并管理心跳检测
  watch(
    [() => props.selectedMessage, () => props.selectedAccount],
    async ([newMessage, newAccount], [oldMessage, oldAccount]) => {
      console.log(
        '🔄 检测到选中消息或账号变化:',
        {
          message: oldMessage?.username,
          account: oldAccount?.name,
          account_id: oldMessage?.account_id,
          self_account_id: oldAccount?.self_account_id,
        },
        '->',
        {
          message: newMessage?.username,
          account: newAccount?.name,
          account_id: newMessage?.account_id,
          self_account_id: newAccount?.self_account_id,
        }
      );

      // 立即停止旧的心跳检测，防止消息串扰
      stopHeartbeat();

      // 检查是否为相同的消息和账号组合
      const isSameSelection =
        newMessage?.account_id === oldMessage?.account_id &&
        newMessage?.platform === oldMessage?.platform &&
        newAccount?.self_account_id === oldAccount?.self_account_id;

      if (isSameSelection) {
        console.log('📌 选中相同的消息和账号，无需重新加载');
        // 即使是相同选择，也要重新启动心跳检测以确保参数正确
        if (newMessage && newAccount) {
          startHeartbeat();
        }
        return;
      }

      if (newMessage && newAccount) {
        console.log('🚀 开始获取新会话的聊天记录:', {
          username: newMessage.username,
          account_id: newMessage.account_id,
          platform: newMessage.platform,
          self_account_id: newAccount.self_account_id,
        });

        // 清空旧消息，避免显示错误的历史记录
        chatMessages.value = [];
        inputMessage.value = '';
        error.value = null;

        // 重置智能滚动相关状态
        lastMessageIds.value = new Set();
        showNewMessageTip.value = false;
        newMessageCount.value = 0;

        // 清理消息引用和高亮状态
        messageRefs.value.clear();
        clearHighlight();

        // 获取新会话的聊天记录
        await getChatMessages(true, false);

        // 启动针对新会话的心跳检测
        startHeartbeat();

        // 滚动到底部
        await nextTick();
        scrollToBottom();
      } else {
        // 如果没有选中消息或账号，清空聊天记录
        chatMessages.value = [];
        inputMessage.value = '';
        error.value = null;

        // 重置智能滚动相关状态
        lastMessageIds.value = new Set();
        showNewMessageTip.value = false;
        newMessageCount.value = 0;

        // 清理消息引用和高亮状态
        messageRefs.value.clear();
        clearHighlight();

        console.log('🧹 清空聊天记录');
      }
    },
    { immediate: true }
  );

  // 组件挂载时的初始化
  onMounted(() => {
    console.log('🎯 ChatWindow 组件已挂载');

    // 如果已经有选中的消息和账号，立即获取聊天记录并启动心跳检测
    if (props.selectedMessage && props.selectedAccount) {
      console.log('🚀 组件挂载时发现已选中会话，开始初始化');
      getChatMessages(true, false);
      startHeartbeat();
    }
  });

  // 组件销毁时清理定时器
  onUnmounted(() => {
    console.log('🔥 ChatWindow 组件销毁，清理心跳检测和相关资源');
    stopHeartbeat();

    // 清理高亮效果定时器
    clearHighlight();

    // 清空所有状态，防止内存泄漏
    chatMessages.value = [];
    inputMessage.value = '';
    error.value = null;
    currentHeartbeatParams.value = null;

    // 清理智能滚动相关状态
    lastMessageIds.value = new Set();
    showNewMessageTip.value = false;
    newMessageCount.value = 0;

    // 清理消息引用映射
    messageRefs.value.clear();
  });

  // 暴露公共方法给父组件
  defineExpose({
    scrollToMessageByTime,
    scrollToBottom,
    scrollToNewMessages,
    clearHighlight,
  });
</script>

<style scoped lang="less">
  // 右侧聊天区域 - flex: 1 自适应，使用浅色背景
  .chat-area {
    flex: 4.5;
    background: var(--color-fill-1);
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 主容器 - 包含聊天窗口和详情面板
    .chat-main-container {
      height: 100%;
      display: flex;
      gap: 1px;
      background: var(--color-border-1);
    }

    .chat-container {
      flex: 2.8;
      display: flex;
      flex-direction: column;
      background: var(--color-fill-1);
      min-width: 400px; // 确保聊天窗口有最小宽度
      position: relative; // 为加载动画提供定位上下文

      // 当详情面板显示时，调整聊天窗口宽度
      // &.with-detail-panel {
      //   flex: 0 0 60%; // 占据60%宽度
      // }
      .chat-normal-view {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .chat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid var(--color-border-1);
        background: var(--color-bg-2);
        flex-shrink: 0;
        height: 40px;

        .chat-user-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .chat-avatar {
            flex-shrink: 0;
            border: 2px solid var(--color-bg-1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            // 当没有头像图片时，显示带背景色的默认头像
            &:not([src]) {
              font-weight: 500;
              font-size: 14px;
            }
          }

          .chat-user-details {
            .chat-username {
              display: block;
              font-weight: 500;
              color: var(--color-text-1);
              font-size: 14px;
              line-height: 1.2;
            }

            .chat-status {
              font-size: 12px;
              color: var(--color-success-6);
              line-height: 1.2;
            }
          }
        }

        .chat-actions {
          flex-shrink: 0;

          .arco-btn {
            transition: all 0.2s ease;
          }
        }
      }

      // ChatWindow内部加载动画样式 - 仅限于聊天窗口区域
      .chat-fullscreen-loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
        background: var(--color-bg-1);
        // 确保加载动画仅在ChatWindow容器内部

        .loading-overlay {
          height: 100%;
          display: flex;
          flex-direction: column;

          // 加载时的头部区域
          .loading-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid var(--color-border-1);
            background: var(--color-bg-2);
            flex-shrink: 0;
            height: 40px;

            .chat-user-info {
              display: flex;
              align-items: center;
              gap: 12px;

              .chat-avatar {
                flex-shrink: 0;
                border: 2px solid var(--color-bg-1);
                transition: all 0.2s ease;
              }

              .chat-user-details {
                .chat-username {
                  display: block;
                  font-weight: 500;
                  color: var(--color-text-1);
                  font-size: 14px;
                  line-height: 1.2;
                }

                .chat-status {
                  font-size: 12px;
                  color: var(--color-success-6);
                  line-height: 1.2;
                }
              }
            }

            .chat-actions {
              flex-shrink: 0;

              .arco-btn {
                transition: all 0.2s ease;

                &.active {
                  background: var(--color-primary-light-1);
                  color: var(--color-primary-6);
                }

                &:hover {
                  background: var(--color-fill-2);
                }
              }
            }
          }

          // 加载内容区域
          .loading-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(180deg, #f8f9fb 0%, #f2f4f7 100%);
            padding: 24px;
            overflow: hidden;

            .loading-container {
              width: 100%;
              max-width: 100%;
              display: flex;
              flex-direction: column;
              gap: 24px;
              height: 100%;
              justify-content: flex-start;
              .skeleton-messages {
                display: flex;
                flex-direction: column;
                gap: 16px;

                .skeleton-message {
                  display: flex;
                  gap: 12px;
                  align-items: flex-start;

                  &.received {
                    justify-content: flex-start;
                  }

                  &.sent {
                    justify-content: flex-end;
                  }

                  .skeleton-avatar {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background: linear-gradient(
                      90deg,
                      #f0f0f0 25%,
                      #e0e0e0 50%,
                      #f0f0f0 75%
                    );
                    background-size: 200% 100%;
                    animation: skeleton-loading 1.5s infinite;
                  }

                  .skeleton-content {
                    flex: 1;
                    max-width: 70%;

                    .skeleton-header {
                      display: flex;
                      align-items: center;
                      gap: 8px;
                      margin-bottom: 4px;

                      .skeleton-username {
                        width: 60px;
                        height: 12px;
                        border-radius: 6px;
                        background: linear-gradient(
                          90deg,
                          #f0f0f0 25%,
                          #e0e0e0 50%,
                          #f0f0f0 75%
                        );
                        background-size: 200% 100%;
                        animation: skeleton-loading 1.5s infinite;
                      }

                      .skeleton-time {
                        width: 40px;
                        height: 10px;
                        border-radius: 5px;
                        background: linear-gradient(
                          90deg,
                          #f0f0f0 25%,
                          #e0e0e0 50%,
                          #f0f0f0 75%
                        );
                        background-size: 200% 100%;
                        animation: skeleton-loading 1.5s infinite;
                      }
                    }

                    .skeleton-bubble {
                      padding: 12px 16px;
                      border-radius: 12px;
                      display: flex;
                      flex-direction: column;
                      gap: 8px;

                      &.received {
                        background: #f7f8fa;
                        border: 1px solid #e1e4e8;
                      }

                      &.sent {
                        background: #0984e3;
                      }

                      .skeleton-text {
                        height: 14px;
                        border-radius: 7px;
                        background: linear-gradient(
                          90deg,
                          #f0f0f0 25%,
                          #e0e0e0 50%,
                          #f0f0f0 75%
                        );
                        background-size: 200% 100%;
                        animation: skeleton-loading 1.5s infinite;

                        &.short {
                          width: 60%;
                        }

                        &.medium {
                          width: 80%;
                        }

                        &.long {
                          width: 90%;
                        }
                      }

                      &.sent .skeleton-text {
                        background: linear-gradient(
                          90deg,
                          rgba(255, 255, 255, 0.3) 25%,
                          rgba(255, 255, 255, 0.5) 50%,
                          rgba(255, 255, 255, 0.3) 75%
                        );
                        background-size: 200% 100%;
                        animation: skeleton-loading 1.5s infinite;
                      }
                    }
                  }
                }
              }

              .loading-text {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                color: var(--color-text-2);
                font-size: 12px;
                width: 100%;

                .loading-icon {
                  font-size: 16px;
                  color: var(--color-primary-6);
                }
              }
            }
          }

          // 加载时的输入区域占位
          .loading-input-placeholder {
            flex-shrink: 0;
            padding: 16px;
            background: var(--color-bg-2);

            .input-placeholder-content {
              background: var(--color-fill-1);
              border-radius: 12px;
              padding: 12px;
              display: flex;
              flex-direction: column;
              gap: 8px;

              .placeholder-toolbar {
                height: 24px;
                background: linear-gradient(
                  90deg,
                  #f0f0f0 25%,
                  #e0e0e0 50%,
                  #f0f0f0 75%
                );
                background-size: 200% 100%;
                animation: skeleton-loading 1.5s infinite;
                border-radius: 6px;
                width: 120px;
              }

              .placeholder-textarea {
                height: 60px;
                background: linear-gradient(
                  90deg,
                  #f0f0f0 25%,
                  #e0e0e0 50%,
                  #f0f0f0 75%
                );
                background-size: 200% 100%;
                animation: skeleton-loading 1.5s infinite;
                border-radius: 8px;
                margin: 8px 0;
              }

              .placeholder-send-btn {
                height: 32px;
                width: 80px;
                background: linear-gradient(
                  90deg,
                  #e3f2fd 25%,
                  #bbdefb 50%,
                  #e3f2fd 75%
                );
                background-size: 200% 100%;
                animation: skeleton-loading 1.5s infinite;
                border-radius: 8px;
                align-self: flex-end;
              }
            }
          }
        }
      }

      .chat-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .emoji-display {
          padding: 16px;
          text-align: center;
          border-bottom: 1px solid var(--color-border-1);
          background: var(--color-fill-1);
          flex-shrink: 0;

          .emoji-large {
            font-size: 36px;
            line-height: 1;
          }
        }

        // 加载更多消息按钮
        .load-more-container {
          display: flex;
          justify-content: center;
          padding: 12px 0;
          border-bottom: 1px solid var(--color-border-1);
          margin-bottom: 16px;

          .load-more-btn {
            color: var(--color-text-3);
            font-size: 12px;
            transition: all 0.2s ease;

            &:hover {
              color: var(--color-primary-6);
              background: var(--color-fill-1);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
        }

        // 聊天消息区域 - 全面重构的消息布局
        .chat-messages {
          flex: 1;
          overflow-y: auto;
          padding: 16px;
          background: linear-gradient(180deg, #f8f9fb 0%, #f2f4f7 100%);
          position: relative; // 为新消息提示提供定位上下文

          // 隐藏滚动条但保持滚动功能
          // -ms-overflow-style: none; // IE 和 Edge

          // &::-webkit-scrollbar {
          //   display: none; // Chrome, Safari, Opera
          // }

          // 修改滚动条颜色
          &::-webkit-scrollbar {
            width: 8px;
          }

          &::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 4px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            transition: background-color 0.15s ease;

            &:hover {
              background: rgba(0, 0, 0, 0.1);
            }
          }

          // 新消息提示浮层
          .new-message-tip {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            cursor: pointer;
            animation: slideUp 0.3s ease-out;

            .tip-content {
              display: flex;
              align-items: center;
              gap: 6px;
              padding: 8px 16px;
              background: var(--color-primary-6);
              color: white;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 500;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              transition: all 0.2s ease;
              white-space: nowrap;

              &:hover {
                background: var(--color-primary-7);
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
              }

              .tip-icon {
                font-size: 12px;
                animation: bounce 1s infinite;
              }

              .tip-text {
                line-height: 1;
              }
            }
          }

          // 错误状态样式
          .chat-error-state {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 16px;
            min-height: 200px;

            .error-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 12px;
              text-align: center;

              .error-icon {
                font-size: 32px;
                color: var(--color-danger-6);
              }

              .error-text {
                font-size: 14px;
                color: var(--color-text-2);
                line-height: 1.4;
                max-width: 200px;
              }
            }
          }

          // 消息包装器
          .chat-message-wrapper {
            margin-bottom: 16px;
            transition: all 0.3s ease-in-out;

            // 高亮效果样式
            &.message-highlighted {
              background: linear-gradient(
                90deg,
                rgba(24, 144, 255, 0.1) 0%,
                rgba(24, 144, 255, 0.05) 50%,
                rgba(24, 144, 255, 0.1) 100%
              );
              border-radius: 8px;
              padding: 8px;
              margin: 8px 0;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              animation: highlight-pulse 2.5s ease-in-out;

              // 高亮动画
              @keyframes highlight-pulse {
                0% {
                  background: rgba(24, 144, 255, 0.2);
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
                }
                50% {
                  background: rgba(24, 144, 255, 0.1);
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                }
                100% {
                  background: rgba(24, 144, 255, 0.05);
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
                }
              }
            }

            // 消息行布局
            .message-row {
              display: flex;
              gap: 8px;
              align-items: flex-start;

              // 接收消息布局
              &.received {
                justify-content: flex-start;

                .message-content {
                  max-width: 70%;
                  align-items: flex-start;

                  .message-header {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 2px;

                    .username {
                      font-size: 12px;
                      color: var(--color-text-3);
                      font-weight: 500;
                    }

                    .message-time {
                      font-size: 11px;
                      color: var(--color-text-3);
                    }
                  }

                  .message-bubble {
                    position: relative;
                    background: var(--color-bg-1);
                    color: var(--color-text-1);
                    border: 1px solid #e1e4e8;
                    text-align: left;
                    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);

                    // 图片消息样式
                    .message-image {
                      border-radius: 8px;
                      max-width: 100%;
                      height: auto;
                      display: block;

                      // 确保图片在气泡内正确显示
                      :deep(.arco-image) {
                        border-radius: 8px;
                        overflow: hidden;
                      }

                      // 图片加载状态
                      :deep(.arco-image-loader) {
                        background: var(--color-fill-2);
                        border-radius: 8px;
                      }

                      // 图片错误状态
                      :deep(.arco-image-error) {
                        background: var(--color-fill-2);
                        color: var(--color-text-3);
                        font-size: 12px;
                        border-radius: 8px;
                        min-height: 100px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      }
                    }

                    // 接收消息状态图标样式
                    .message-status-icon {
                      position: absolute;
                      right: -20px;
                      bottom: 0;
                    }
                  }
                }
              }

              // 发送消息布局
              &.sent {
                justify-content: flex-end;

                .message-content {
                  max-width: 70%;

                  .message-header {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 2px;
                    justify-content: flex-end;

                    .username {
                      font-size: 12px;
                      color: var(--color-text-3);
                      font-weight: 500;
                    }

                    .message-time {
                      font-size: 11px;
                      color: var(--color-text-3);
                    }
                  }

                  .message-bubble {
                    position: relative;
                    background: #0984e3;
                    color: #ffffff;
                    border: none;
                    text-align: left;
                    box-shadow: 0 3px 12px rgba(116, 185, 255, 0.25);

                    // 图片消息样式（发送消息）
                    .message-image {
                      border-radius: 8px;
                      max-width: 100%;
                      height: auto;
                      display: block;

                      // 确保图片在蓝色气泡内正确显示
                      :deep(.arco-image) {
                        border-radius: 8px;
                        overflow: hidden;
                      }

                      // 图片加载状态（发送消息）
                      :deep(.arco-image-loader) {
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 8px;
                      }

                      // 图片错误状态（发送消息）
                      :deep(.arco-image-error) {
                        background: rgba(255, 255, 255, 0.1);
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 12px;
                        border-radius: 8px;
                        min-height: 100px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      }
                    }

                    // 发送消息状态图标样式
                    .message-status-icon {
                      position: absolute;
                      left: -20px;
                      bottom: 0;
                    }
                  }
                }
              }

              // 头像样式
              .message-avatar {
                flex-shrink: 0;

                .arco-avatar {
                  border: 1px solid var(--color-bg-1);
                  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                  // 当没有头像图片时，显示带背景色的默认头像
                  &:not([src]) {
                    font-weight: 500;
                    font-size: 12px;
                  }
                }
              }

              .message-content {
                display: flex;
                flex-flow: column;
                align-items: end;
                // 通用消息气泡样式
                .message-bubble {
                  padding: 8px 12px;
                  border-radius: 12px;
                  font-size: 14px;
                  line-height: 1.5;
                  word-wrap: break-word;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                  position: relative;
                  max-width: max-content;

                  // 图片消息时减少内边距
                  &:has(.message-image) {
                    padding: 4px;
                  }

                  // 通用图片消息样式
                  .message-image {
                    border-radius: 8px;
                    max-width: 200px;
                    max-height: 150px;
                    width: auto;
                    height: auto;
                    display: block;
                    object-fit: cover;

                    // 响应式图片尺寸
                    @media (max-width: 768px) {
                      max-width: 150px;
                      max-height: 120px;
                    }
                  }

                  .success_icon {
                    color: rgb(var(--success-6));
                  }
                }
              }
            }
          }
        }
      }

      // 聊天输入模块 - 整合优化，统一设计风格
      .chat-input-module {
        flex-shrink: 0;

        // 主输入区域
        .input-main-area {
          padding: 10px 10px 5px;
          background: var(--color-bg-2);
          margin: 0 20px 10px;
          border-radius: var(--border-radius-large);
          box-shadow: 0 0px 4px rgba(0, 0, 0, 0.05);
          // animation: border-breathing 2.5s ease-in-out infinite;

          .input-toolbar {
            display: flex;
            gap: 8px;
            padding-left: 8px;

            .toolbar-btn {
              border-radius: 8px;
              transition: all 0.2s ease;

              &:hover {
                transform: translateY(-1px);
              }
              width: 20px;
              height: 20px;
              cursor: pointer;
            }

            .emoji-toggle-btn {
              color: var(--color-text-2);

              &.active {
                color: var(--color-primary-6);
                background: var(--color-primary-light-1);
              }

              &:hover {
                color: var(--color-primary-6);
                background: var(--color-fill-2);
              }
            }
          }

          // 表情包选择面板
          .emoji-panel {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background: var(--color-bg-1);
            border: 1px solid var(--color-border-2);
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            margin-bottom: 8px;
            max-height: 200px;
            overflow: hidden;

            .emoji-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              border-bottom: 1px solid var(--color-border-1);
              background: var(--color-bg-2);

              .emoji-title {
                font-size: 12px;
                font-weight: 500;
                color: var(--color-text-2);
              }

              .emoji-close-btn {
                color: var(--color-text-3);

                &:hover {
                  color: var(--color-text-1);
                  background: var(--color-fill-2);
                }
              }
            }

            .emoji-grid {
              display: grid;
              grid-template-columns: repeat(8, 1fr);
              gap: 4px;
              padding: 8px;
              max-height: 150px;
              overflow-y: auto;

              .emoji-item {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 18px;
                transition: all 0.2s ease;

                &:hover {
                  background: var(--color-fill-2);
                  transform: scale(1.1);
                }

                &:active {
                  transform: scale(0.95);
                }
              }
            }
          }

          .input-content {
            position: relative;
            display: flex;
            gap: 12px;
            align-items: flex-end;
            margin-top: 5px;

            .message-textarea {
              flex: 1;
              border-radius: 12px;
              border: none;
              background: var(--color-bg-1);
              font-size: 14px;
              line-height: 1.5;
              transition: all 0.2s ease;

              &:focus {
                border-color: var(--color-primary-6);
                box-shadow: 0 0 0 2px rgba(var(--primary-6), 0.1);
              }

              // 隐藏滚动条
              -ms-overflow-style: none;

              &::-webkit-scrollbar {
                display: none;
              }

              // 深度选择器，确保内部元素也隐藏滚动条
              :deep(.arco-textarea) {
                -ms-overflow-style: none;
                scrollbar-width: none;

                &::-webkit-scrollbar {
                  display: none;
                }
              }

              :deep(textarea) {
                -ms-overflow-style: none;
                scrollbar-width: none;

                &::-webkit-scrollbar {
                  display: none;
                }
              }

              // 确保 Arco Design 内部组件也应用动画效果
              &:focus-within {
                :deep(.arco-textarea) {
                  border: none !important;
                  box-shadow: none !important;
                }
              }
            }

            .send-action {
              position: absolute;
              right: 5px;
              bottom: 5px;
              z-index: 999;

              .send-btn {
                border-radius: 12px;
                padding: 8px 20px;
                font-weight: 500;
                box-shadow: 0 2px 4px rgba(var(--primary-6), 0.3);
                transition: all 0.2s ease;

                &:hover:not(:disabled) {
                  transform: translateY(-1px);
                  box-shadow: 0 4px 8px rgba(var(--primary-6), 0.4);
                }

                // 发送成功状态
                &.success {
                  background: var(--color-success-6);
                  border-color: var(--color-success-6);
                  box-shadow: 0 2px 4px rgba(var(--success-6), 0.3);

                  &:hover {
                    background: var(--color-success-5);
                    border-color: var(--color-success-5);
                  }
                }

                // 发送失败状态
                &.failed {
                  background: var(--color-danger-6);
                  border-color: var(--color-danger-6);
                  box-shadow: 0 2px 4px rgba(var(--danger-6), 0.3);

                  &:hover {
                    background: var(--color-danger-5);
                    border-color: var(--color-danger-5);
                  }
                }

                // 禁用状态
                &:disabled {
                  opacity: 0.6;
                  cursor: not-allowed;
                  transform: none;
                  box-shadow: none;
                }
              }
            }
          }
        }

        // 底部信息区域
        .input-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 20px;
          background: var(--color-fill-1);
          font-size: 11px;

          .auto-reply-text {
            color: var(--color-text-3);
            flex: 1;
          }
        }
      }
    }

    .chat-placeholder {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .placeholder-content {
        text-align: center;
        color: var(--color-text-3);

        .placeholder-icon {
          width: 40px;
          height: 40px;
          margin-bottom: 12px;
          opacity: 0.5;
        }

        p {
          font-size: 14px;
          margin: 0;
        }
      }
    }
  }

  // 边框呼吸动画 - 聚焦时的微妙呼吸效果
  @keyframes border-breathing {
    0% {
      border-color: var(--color-primary-6);
      box-shadow: 0 0 0 1px rgba(var(--primary-6), 0.1);
    }
    50% {
      border-color: var(--color-primary-5);
      box-shadow: 0 0 0 1px rgba(var(--primary-5), 1);
    }
    100% {
      border-color: var(--color-primary-6);
      box-shadow: 0 0 0 1px rgba(var(--primary-6), 0.1);
    }
  }

  // 响应式优化 - 在小屏幕设备上减少动画强度
  @media (max-width: 768px) {
    .message-textarea {
      &:focus {
        // 在移动设备上使用更轻微的动画效果
        animation: border-breathing-mobile 2.5s ease-in-out infinite;
      }
    }
  }

  // 移动端的轻微呼吸动画
  @keyframes border-breathing-mobile {
    0% {
      border-color: var(--color-primary-6);
      box-shadow: 0 0 0 1px rgba(var(--primary-6), 0.08);
    }
    50% {
      border-color: var(--color-primary-5);
      box-shadow: 0 0 0 1px rgba(var(--primary-5), 0.12);
    }
    100% {
      border-color: var(--color-primary-6);
      box-shadow: 0 0 0 1px rgba(var(--primary-6), 0.08);
    }
  }

  // 骨架屏加载动画
  @keyframes skeleton-loading {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  // 新消息提示滑入动画
  @keyframes slideUp {
    0% {
      opacity: 0;
      transform: translateX(-50%) translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  // 图标弹跳动画
  @keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-3px);
    }
    60% {
      transform: translateY(-2px);
    }
  }

  // 尊重用户的动画偏好设置
  @media (prefers-reduced-motion: reduce) {
    .message-textarea {
      &:focus {
        // 如果用户偏好减少动画，则禁用呼吸动画
        animation: none;
        // 保持静态的聚焦效果
        border-color: var(--color-primary-6);
        box-shadow: 0 0 0 2px rgba(var(--primary-6), 0.1);
      }
    }

    // 禁用骨架屏动画
    .skeleton-avatar,
    .skeleton-username,
    .skeleton-time,
    .skeleton-text {
      animation: none !important;
      background: #f0f0f0 !important;
    }

    .skeleton-bubble.sent .skeleton-text {
      background: rgba(255, 255, 255, 0.4) !important;
    }
  }
</style>
